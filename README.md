# Fitness Workout Tracker, <PERSON><PERSON> Planner, Sleep Tracker App UI UX Design Convent Into Flutter Code

# codeforany @codeforany

- [Youtube Full Playlist: Fitness Workout Tracker, Meal Planner, Sleep Tracker App UI UX Design Convent Into Flutter Code](https://www.youtube.com/playlist?list=PLzcRC7PA0xWR1AY-uvplpAYoDFzRdUHgQ)
- [Youtube Channel: @codeforany](https://www.youtube.com/channel/UCdQTp9wRK5vAOlEQZf9PHSg)
- [Youtube Channel Subscribe: @codeforany](https://www.youtube.com/channel/UCdQTp9wRK5vAOlEQZf9PHSg?sub_confirmation=1)

- [Youtube Video Part-1: App Induction](https://youtu.be/GZzMnrGNZEA)
- [Youtube Video Part-2: Get Startup And Signup UI](https://youtu.be/pDoH7oheZRk)
- [Youtube Video Part-3: Complete Profile UI, Goal UI, Login UI](https://youtu.be/GrINadeF1Ic)
- [Youtube Video Part-4: Bottom Tab Bar UI With Floating Button](https://youtu.be/JYJbK7vTCJk)
- [Youtube Video Part-5: Home Tab-1 TabView UI With Pie Chart](https://youtu.be/jd6C5qCQ0B4)
- [Youtube Video Part-6: Home Tab-2 Line Chart Activity Status](https://youtu.be/VwikPX-9_rs)
- [Youtube Video Part-7: Home Tab-3 Workout Progress Line Chart](https://youtu.be/UX0UuPx8aRg)
- [Youtube Video Part-8: Activity Tracker UI And Notification UI](https://youtu.be/OjvOxsVVJSo)
- [Youtube Video Part-9: Profile Tab UI And Finished Workout UI](https://youtu.be/cF-x4xq99fw)
- [Youtube Video Part-10: Workout Tracker Tab UI](https://youtu.be/AUIR0RKRQwo)
- [Youtube Video Part-11: Workout Details UI Screen](https://youtu.be/ftAi1kfXObk)
- [Youtube Video Part-12: Exercises Step Details UI Screen](https://youtu.be/3Dfn54U340k)
- [Youtube Video Part-13: Workout Schedule UI, Calendar Timeline UI ](https://youtu.be/vARI416CLUA)
- [Youtube Video Part-14: Workout Schedule Timeline UI](https://youtu.be/G2jFJ3-HmkU)
- [Youtube Video Part-15: Add Workout Schedule UI And Mark Done](https://youtu.be/LL52gqRlMs8)
- [Youtube Video Part-16: Meals Planner UI With Line Chart UI](https://youtu.be/SqAwLgftzBI)
- [Youtube Video Part-17: Meals Food Details UI Screen](https://youtu.be/ppzr1VOT51s)
- [Youtube Video Part-18: Food Info Recipe Details UI Screen](https://youtu.be/isu4tYpcwcI)
- [Youtube Video Part-19: Meal Schedule UI Screen](https://youtu.be/Gvhz0PZIrTs)
- [Youtube Video Part-20: Sleep Tracker UI Screen](https://youtu.be/8QrKRt3Avkc)
- [Youtube Video Part-21: Sleep Schedule And Add Alarm UI Screen](https://youtu.be/GacvUiYp0uU)
- [Youtube Video Part-22: Progress Photos Tab UI Screen](https://youtu.be/0HC306fRSg0)
- [Youtube Video Part-23: Comparison UI and Result UI Screen](https://youtu.be/puds7ztrQ-c)


UI UX App Design by: [Pixel True](https://www.pixeltrue.com/free-ui-kits/fitness-app-ui-kit)

A new Flutter project.

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
